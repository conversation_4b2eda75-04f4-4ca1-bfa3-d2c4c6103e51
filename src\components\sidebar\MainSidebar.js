import React, { useState, useEffect, useCallback } from 'react';
import './MainSidebar.css';
import './ModernSidebar.css'; // Import the modern sidebar styles
import MapSelector from '../maps/MapSelector';
import { useCameraStore } from '../../store/cameraStore';
import SidebarItem from './SidebarItem';
import SidebarDropdownItem from './SidebarDropdownItem.jsx'; // Import the SidebarDropdownItem component
import DraggableSidebarCamera from './DraggableSidebarCamera'; // Import the draggable camera component
import {
  Videocam as CctvIcon,
  Bookmark as BookmarkIcon,
  Collections as CollectionIcon,
  LocationOn as LocationIcon,
  VideoLibrary as StreamIcon
} from '@mui/icons-material';

function MainSidebar({ onViewChange, isSidebarExpanded = false }) {
  const [collectionsExpanded, setCollectionsExpanded] = useState(true);
  const [showBookmark, setShowBookmark] = useState(false);
  const [showMap, setShowMap] = useState(false);
  const [selectedMap, setSelectedMap] = useState(null);
  const [expandedCollections, setExpandedCollections] = useState({});

  const {
    collections,
    activeCollection,
    setCollectionActive,
    getCamerasByCollection,
    getBookmarkedCameras,
    isBookmarked,
    cameras
  } = useCameraStore();

  // Force re-render when bookmarks or cameras change
  useEffect(() => {
    setCollectionsExpanded(prev => prev); // Just trigger a re-render if needed
  }, [cameras, collections]);

  // Initialize all collections to be expanded by default
  useEffect(() => {
    if (collections.length > 0) {
      const initialExpandedState = {};
      collections.forEach(collection => {
        initialExpandedState[collection.id] = true;
      });
      setExpandedCollections(initialExpandedState);
    }
  }, [collections]);

  const handleMapSelect = (mapType) => {
    setSelectedMap(mapType);
    onViewChange(mapType === 'basic' ? 'basic-map' : 'global-map');
  };

  // We'll call onCreateCollection directly when needed

  const handleSelectCollection = (collectionId) => {
    setCollectionActive(collectionId);
    onViewChange('camera');
  };

  const toggleCollectionExpand = (collectionId, event) => {
    event.stopPropagation(); // Prevent triggering the collection selection
    setExpandedCollections(prev => ({
      ...prev,
      [collectionId]: !prev[collectionId]
    }));
  };

  const handleBookmarkClick = useCallback((camera) => {
    // If camera is provided, we're clicking on a specific bookmark
    if (camera) {
      console.log('Clicked on bookmarked camera:', camera.name, camera.id);
      // You could add specific camera handling here if needed
      // For now, just switch to bookmark view
    }
    setCollectionActive(null);
    setShowBookmark(false);
    onViewChange('bookmark');
  }, [onViewChange, setCollectionActive]);

  const handleBookmarkSectionClick = useCallback(() => {
    setShowBookmark((prev) => !prev);
    if (!showBookmark) {
      onViewChange('bookmark');
    }
  }, [showBookmark, onViewChange]);

  // Bookmark removal is now handled directly in the dropdown items

  // These functions are no longer needed with the new UI components

  return (
    <div className="universal-sidebar-content">
      {/* Collection Section */}
      <ul className="sidebar-menu">
        <li className={`menu-item has-children ${collectionsExpanded ? 'expanded' : ''}`}>
          <button
            className="menu-label"
            onClick={() => {
              setCollectionsExpanded(prev => !prev);
              onViewChange('camera');
            }}
            aria-expanded={collectionsExpanded}
            aria-haspopup="true"
          >
            <div className="menu-icon">
              <CollectionIcon />
            </div>
            <span>Collections</span>
            <span className="chevron">▾</span>
          </button>
          <ul className="submenu">
            {collections.map(collection => {
              const cameras = getCamerasByCollection(collection.id);
              return (
                <li key={collection.id} className={`submenu-item has-children ${expandedCollections[collection.id] ? 'expanded' : ''}`}>
                  <button
                    className={`submenu-label ${activeCollection === collection.id ? 'active' : ''}`}
                    onClick={() => handleSelectCollection(collection.id)}
                    aria-expanded={expandedCollections[collection.id]}
                    aria-haspopup={cameras.length > 0 ? "true" : undefined}
                  >
                    <div className="submenu-icon">
                      <CollectionIcon />
                    </div>
                    <span>{collection.name}</span>
                    {cameras.length > 0 && (
                      <span className="submenu-count">({cameras.length})</span>
                    )}
                    {cameras.length > 0 && (
                      <span
                        className="chevron"
                        onClick={(e) => toggleCollectionExpand(collection.id, e)}
                      >▾</span>
                    )}
                  </button>
                  {cameras.length > 0 && (
                    <ul className="subsubmenu">
                      {cameras.map(camera => (
                        <DraggableSidebarCamera
                          key={camera.id}
                          camera={camera}
                          isBookmarked={isBookmarked(camera.id)}
                          onClick={(camera) => {
                            // Handle camera selection if needed
                            console.log('Camera selected:', camera.name);
                          }}
                        />
                      ))}
                    </ul>
                  )}
                </li>
              );
            })}
          </ul>
        </li>
      </ul>

      {/* Bookmarks Section */}
      <SidebarItem
        icon={<BookmarkIcon />}
        label="Bookmarks"
        isActive={showBookmark}
        onClick={handleBookmarkSectionClick}
        isExpandable={true}
        isExpanded={showBookmark}
        count={getBookmarkedCameras().length}
        isSidebarExpanded={isSidebarExpanded}
      >
        {getBookmarkedCameras().length === 0 ? (
          <div className="universal-sidebar-message">No Bookmarks Yet</div>
        ) : (
          getBookmarkedCameras().map(camera => (
            <SidebarDropdownItem
              key={camera.id}
              icon={<CctvIcon />}
              label={camera.name}
              onClick={() => handleBookmarkClick(camera)}
              className="bookmark-item"
              isSidebarExpanded={isSidebarExpanded}
            />
          ))
        )}
      </SidebarItem>

      {/* Map Section */}
      <SidebarItem
        icon={<LocationIcon />}
        label="Map"
        isActive={showMap}
        onClick={() => setShowMap((prev) => !prev)}
        isExpandable={true}
        isExpanded={showMap}
        isSidebarExpanded={isSidebarExpanded}
      >
        <MapSelector onMapSelect={handleMapSelect} selectedMap={selectedMap} />
      </SidebarItem>

      {/* External Systems Section - Commented out
      <SidebarItem
        icon={<img src={externalIcon} alt="" />}
        label="External Systems"
        isActive={showExternalSystem}
        onClick={() => {
          setShowExternalSystem((prev) => !prev);
          onViewChange('external-system');
        }}
        isExpandable={true}
        isExpanded={showExternalSystem}
        isSidebarExpanded={true}
      >
        <div className="universal-sidebar-message">You have not added any external devices yet.</div>
        <SidebarDropdownItem
          icon={<img src={addIcon} alt="" />}
          label="Add External Device"
          className="add-device-btn"
        />
      </SidebarItem>
      */}
    </div>
  );
}

export default MainSidebar;

